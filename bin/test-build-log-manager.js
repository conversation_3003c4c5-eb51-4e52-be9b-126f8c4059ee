#!/usr/bin/env node

const chalk = require('chalk');
const BuildLogManager = require('../src/domain/dev-run-fix/BuildLogManager');

/**
 * 测试 BuildLogManager 的日志处理功能
 */
function testBuildLogManager() {
  console.log(chalk.blue('🧪 测试 BuildLogManager 日志处理功能'));
  
  // 模拟你提供的原始日志
  const rawLog = `> vue-element-admin@4.4.0 dev
> vue-cli-service serve

 INFO  Starting development server...
[3%] setup (watch run)
[2K[1A[2K[G[3%] setup (watch run vue-loader-plugin)
[2K[1A[2K[G[3%] setup (watch run ESLintWebpackPlugin_1)
[2K[1A[2K[G[3%] setup (watch run webpack-dev-middleware)
[2K[1A[2K[G[3%] setup (watch run)
[2K[1A[2K[G[4%] setup (normal module factory)
[2K[1A[2K[G[51%] building (3574/3574 modules)
[2K[1A[2K[G[65%] building 
[2K[1A[2K[G[69%] building (finish)
[2K[1A[2K[G[95%] emitting (emit)
[2K[1A[2K[G[98%] emitting (after emit)
[2K[1A[2K[GBuild finished at 15:33:57 by 0.000s
[2K[1A[2K[GBuild finished at 15:33:57 by 0.000s
[2K[1A[2K[G ERROR  Failed to compile with 1 error3:33:57 PM

[eslint] .eslintrc.js:
	Configuration for rule "vue/max-attributes-per-line" is invalid:
	Value {"max":1,"allowFirstLine":false} should be number.
	Value {"max":1,"allowFirstLine":false} should NOT have additional properties.
	Value {"max":1,"allowFirstLine":false} should match exactly one schema in oneOf.


You may use special comments to disable some warnings.
Use // eslint-disable-next-line to ignore the next line.
Use /* eslint-disable */ to ignore all warnings in a file.
Build finished at 15:33:57 by 0.000s
[2K[1A[2K[GBuild finished at 15:33:57 by 0.000s
[2K[1A[2K[GERROR in [eslint] .eslintrc.js:
	Configuration for rule "vue/max-attributes-per-line" is invalid:
	Value {"max":1,"allowFirstLine":false} should be number.
	Value {"max":1,"allowFirstLine":false} should NOT have additional properties.
	Value {"max":1,"allowFirstLine":false} should match exactly one schema in oneOf.

Name compiled with 1 error
Debugger listening on ws://127.0.0.1:49204/664a69cf-468f-4f98-a851-a6dd115826e8
For help, see: https://nodejs.org/en/docs/inspector
Debugger attached.
[1m[33m[@vue/compiler-sfc][0m[33m ::v-deep usage as a combinator has been deprecated. Use :deep(<inner-selector>) instead of ::v-deep <inner-selector>.[0m

[1m[33m[@vue/compiler-sfc][0m[33m ::v-deep usage as a combinator has been deprecated. Use :deep(<inner-selector>) instead of ::v-deep <inner-selector>.[0m`;

  console.log(chalk.gray('\n📝 原始日志长度:'), rawLog.length, '字符');
  console.log(chalk.gray('📝 原始日志行数:'), rawLog.split('\n').length, '行');

  // 创建 BuildLogManager 实例
  const logManager = new BuildLogManager({
    verbose: true,
    showProgress: false,
    showWarnings: true
  });

  console.log(chalk.yellow('\n🔄 处理日志...'));
  
  // 处理日志
  const result = logManager.processRawLog(rawLog);
  
  console.log(chalk.green('\n✅ 处理完成！'));
  console.log(chalk.gray('📊 处理结果统计:'));
  console.log(`   清理后长度: ${result.cleanedOutput.length} 字符`);
  console.log(`   重要行数: ${result.importantLines.length} 行`);
  console.log(`   错误数量: ${result.summary.errors.length}`);
  console.log(`   警告数量: ${result.summary.warnings.length}`);
  console.log(`   信息数量: ${result.summary.info.length}`);
  console.log(`   编译进度: ${result.summary.progress}%`);
  console.log(`   有编译错误: ${result.summary.hasCompilationError}`);

  console.log(chalk.blue('\n📋 重要日志行:'));
  result.importantLines.forEach((line, index) => {
    console.log(chalk.gray(`   ${index + 1}. ${line}`));
  });

  console.log(chalk.blue('\n🔥 错误信息:'));
  result.summary.errors.forEach((error, index) => {
    console.log(chalk.red(`   ${index + 1}. ${error}`));
  });

  console.log(chalk.blue('\n⚠️  警告信息:'));
  result.summary.warnings.forEach((warning, index) => {
    console.log(chalk.yellow(`   ${index + 1}. ${warning}`));
  });

  console.log(chalk.blue('\n📄 格式化输出:'));
  console.log('---');
  console.log(logManager.formatImportantOutput(result));
  console.log('---');

  // 测试颜色检测
  console.log(chalk.blue('\n🎨 测试颜色检测:'));
  testColorDetection(logManager);

  // 测试过滤功能
  console.log(chalk.blue('\n🔍 测试过滤功能:'));
  testFiltering(logManager);
}

function testColorDetection(logManager) {
  const testLines = [
    '\x1b[31mERROR: This is a red error\x1b[0m',
    '\x1b[33mWARNING: This is a yellow warning\x1b[0m',
    '\x1b[32mSUCCESS: This is a green success\x1b[0m',
    '\x1b[1;33m[@vue/compiler-sfc]\x1b[0m\x1b[33m ::v-deep usage as a combinator has been deprecated.\x1b[0m',
    'Normal text without colors'
  ];

  testLines.forEach(line => {
    const processed = logManager.processLine(line);
    if (processed) {
      console.log(`   ${processed.level.padEnd(8)} | ${processed.cleaned}`);
    }
  });
}

function testFiltering(logManager) {
  const testLines = [
    '[24%] building (1/17 modules)',
    '[2K[1A[2K[G[3%] setup (watch run vue-loader-plugin)',
    'Build finished at 15:33:57 by 0.000s',
    'ERROR  Failed to compile with 1 error',
    'Configuration for rule "vue/max-attributes-per-line" is invalid',
    'Starting development server...',
    'Local: http://localhost:8080',
    'Debugger listening on ws://127.0.0.1:49204'
  ];

  console.log('   测试行过滤 (显示保留的行):');
  testLines.forEach(line => {
    const processed = logManager.processLine(line);
    if (processed) {
      const importance = processed.isImportant ? '🔥' : '📝';
      console.log(`   ${importance} [${processed.level}] ${processed.cleaned}`);
    } else {
      console.log(chalk.gray(`   ❌ [过滤] ${line}`));
    }
  });
}

// 运行测试
testBuildLogManager();
