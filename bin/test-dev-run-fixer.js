#!/usr/bin/env node

const path = require('path');
const chalk = require('chalk');
const DevRunFixer = require('../src/domain/dev-run-fix/DevRunFixer');

/**
 * 测试 DevRunFixer 的改进版本
 * 使用 DevServerManager 来启动和停止项目
 * 改进日志处理，去掉颜色字符和噪音日志
 */
async function testDevRunFixer() {
  // 测试项目路径
  const testProjectPath = '/Users/<USER>/works/galaxy/vue-element-admin';

  console.log(chalk.blue('🧪 测试 DevRunFixer 改进版本'));
  console.log(chalk.gray(`   测试项目: ${testProjectPath}`));
  console.log(chalk.gray(`   改进内容:`));
  console.log(chalk.gray(`   - 使用 DevServerManager 管理服务器`));
  console.log(chalk.gray(`   - 清理日志中的颜色字符和噪音`));
  console.log(chalk.gray(`   - 改进错误捕获和分析`));

  // 检查项目是否存在
  const fs = require('fs-extra');
  if (!await fs.pathExists(testProjectPath)) {
    console.log(chalk.red(`❌ 测试项目不存在: ${testProjectPath}`));
    console.log(chalk.yellow('请确保项目路径正确，或者修改 testProjectPath 变量'));
    return;
  }

  const packageJsonPath = path.join(testProjectPath, 'package.json');
  if (!await fs.pathExists(packageJsonPath)) {
    console.log(chalk.red(`❌ 项目中没有 package.json: ${packageJsonPath}`));
    return;
  }

  console.log(chalk.green('✅ 项目路径验证通过'));

  try {
    // 创建 DevRunFixer 实例
    const devRunFixer = new DevRunFixer(testProjectPath, {
      maxAttempts: 2,
      devCommand: 'npm run dev',
      timeout: 90000, // 90秒超时，给足够时间
      verbose: true,
      port: 9527 // vue-element-admin 默认端口
    });

    console.log(chalk.yellow('\n🚀 开始测试开发服务器启动...'));
    
    // 尝试修复开发服务器
    const result = await devRunFixer.fixDevServer();
    
    if (result.success) {
      console.log(chalk.green('\n✅ 测试成功！'));
      console.log(chalk.gray(`   尝试次数: ${result.attempts}`));
      console.log(chalk.gray(`   服务器地址: ${result.baseUrl}`));
      console.log(chalk.gray(`   修改文件数: ${result.stats.filesModified}`));
      console.log(chalk.gray(`   修复错误数: ${result.stats.errorsFixed}`));
      
      // 等待几秒钟让用户看到结果
      console.log(chalk.yellow('\n⏳ 服务器将在 5 秒后停止...'));
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // 停止服务器
      await devRunFixer.stopDevServer();
      console.log(chalk.green('✅ 服务器已停止'));
      
    } else {
      console.log(chalk.red('\n❌ 测试失败'));
      console.log(chalk.red(`   错误: ${result.error}`));
      console.log(chalk.gray(`   尝试次数: ${result.attempts}`));
      console.log(chalk.gray(`   修改文件数: ${result.stats.filesModified}`));
      console.log(chalk.gray(`   修复错误数: ${result.stats.errorsFixed}`));
    }
    
    // 显示详细统计信息
    const stats = devRunFixer.getStats();
    console.log(chalk.blue('\n📊 详细统计信息:'));
    console.log(JSON.stringify(stats, null, 2));
    
  } catch (error) {
    console.error(chalk.red(`\n💥 测试过程中出现异常: ${error.message}`));
    console.error(error.stack);
  }
}

// 处理命令行参数
if (process.argv.length > 2) {
  const customPath = process.argv[2];
  console.log(chalk.yellow(`使用自定义项目路径: ${customPath}`));
  // 这里可以修改 testProjectPath
}

// 运行测试
testDevRunFixer().catch(error => {
  console.error(chalk.red('测试失败:'), error);
  process.exit(1);
});
