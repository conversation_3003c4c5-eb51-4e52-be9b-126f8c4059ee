const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { spawn } = require('child_process');
const BuildFixAgent = require('../build-fix/BuildFixAgent');

/**
 * DevRunFixer - 开发服务器启动错误修复器
 *
 * 专门处理 npm run dev 失败的问题，包括：
 * - ESLint 配置错误
 * - Vue 编译器警告和错误
 * - 依赖缺失或版本冲突
 * - 配置文件格式错误
 */
class DevRunFixer {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      maxAttempts: 3,
      devCommand: 'npm run dev',
      timeout: 60000, // 60秒超时
      verbose: false,
      dryRun: false,
      ...options
    };

    // 初始化 BuildFixAgent 来处理文件修复
    this.buildFixAgent = new BuildFixAgent(projectPath, {
      ...options,
      logDir: options.logDir || path.join(projectPath, 'logs', 'dev-run-fix')
    });

    this.fixStats = {
      attempts: 0,
      errorsFixed: 0,
      filesModified: 0
    };
  }

  /**
   * 尝试启动开发服务器并修复错误
   */
  async fixDevServer() {
    console.log(chalk.blue('🔧 开始修复开发服务器启动问题...'));

    for (let attempt = 1; attempt <= this.options.maxAttempts; attempt++) {
      console.log(chalk.gray(`\n📝 尝试 ${attempt}/${this.options.maxAttempts}: 启动开发服务器`));

      this.fixStats.attempts++;

      const result = await this.tryStartDevServer();

      if (result.success) {
        console.log(chalk.green('✅ 开发服务器启动成功！'));
        return {
          success: true,
          attempts: attempt,
          stats: this.fixStats
        };
      }

      if (attempt < this.options.maxAttempts) {
        console.log(chalk.yellow(`⚠️  第 ${attempt} 次尝试失败，开始分析和修复错误...`));

        const fixResult = await this.analyzeAndFixErrors(result.error, result.output);

        if (!fixResult.success) {
          console.log(chalk.red(`❌ 第 ${attempt} 次修复失败: ${fixResult.error}`));
        } else {
          console.log(chalk.green(`✅ 第 ${attempt} 次修复完成，修改了 ${fixResult.filesModified} 个文件`));
          this.fixStats.filesModified += fixResult.filesModified;
          this.fixStats.errorsFixed += fixResult.errorsFixed || 0;
        }
      }
    }

    return {
      success: false,
      error: `经过 ${this.options.maxAttempts} 次尝试仍无法启动开发服务器`,
      attempts: this.options.maxAttempts,
      stats: this.fixStats
    };
  }

  /**
   * 尝试启动开发服务器
   */
  async tryStartDevServer() {
    return new Promise((resolve) => {
      const [cmd, ...args] = this.options.devCommand.split(' ');

      if (this.options.verbose) {
        console.log(chalk.gray(`   执行命令: ${this.options.devCommand}`));
      }

      const process = spawn(cmd, args, {
        cwd: this.projectPath,
        stdio: 'pipe'
      });

      let output = '';
      let errorOutput = '';
      let hasStarted = false;

      // 设置超时
      const timeout = setTimeout(() => {
        if (!hasStarted) {
          process.kill();
          resolve({
            success: false,
            error: '开发服务器启动超时',
            output: output + errorOutput
          });
        }
      }, this.options.timeout);

      process.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;

        // 检查是否成功启动
        if (this.isDevServerStarted(text)) {
          hasStarted = true;
          clearTimeout(timeout);
          process.kill();
          resolve({
            success: true,
            output: output + errorOutput
          });
        }
      });

      process.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;

        // 检查是否有致命错误
        if (this.isFatalError(text)) {
          clearTimeout(timeout);
          process.kill();
          resolve({
            success: false,
            error: '开发服务器启动失败',
            output: output + errorOutput
          });
        }
      });

      process.on('close', (code) => {
        clearTimeout(timeout);
        if (!hasStarted) {
          resolve({
            success: false,
            error: `开发服务器进程退出，退出码: ${code}`,
            output: output + errorOutput
          });
        }
      });

      process.on('error', (error) => {
        clearTimeout(timeout);
        resolve({
          success: false,
          error: `启动开发服务器时出错: ${error.message}`,
          output: output + errorOutput
        });
      });
    });
  }

  /**
   * 检查开发服务器是否已启动
   */
  isDevServerStarted(output) {
    const successPatterns = [
      /Local:\s+http:\/\/localhost:\d+/,
      /App running at:/,
      /webpack compiled/,
      /Compiled successfully/,
      /ready - started server on/
    ];

    return successPatterns.some(pattern => pattern.test(output));
  }

  /**
   * 检查是否有致命错误
   */
  isFatalError(output) {
    const fatalPatterns = [
      /ERROR\s+Failed to compile/,
      /Configuration for rule .* is invalid/,
      /Module not found/,
      /Cannot resolve module/,
      /SyntaxError:/,
      /TypeError:/,
      /ReferenceError:/
    ];

    return fatalPatterns.some(pattern => pattern.test(output));
  }

  /**
   * 分析错误并尝试修复
   */
  async analyzeAndFixErrors(error, output) {
    try {
      // 识别错误类型
      const errorType = this.identifyErrorType(output);

      if (this.options.verbose) {
        console.log(chalk.gray(`   🔍 识别的错误类型: ${errorType}`));
      }

      switch (errorType) {
        case 'eslint-config':
          return await this.fixEslintConfigError(output);
        case 'dependency-missing':
          return await this.fixDependencyError(output);
        case 'syntax-error':
          return await this.fixSyntaxError(output);
        default:
          return await this.fixGenericError(output);
      }
    } catch (error) {
      return {
        success: false,
        error: `分析错误失败: ${error.message}`
      };
    }
  }

  /**
   * 识别错误类型
   */
  identifyErrorType(output) {
    // ESLint 配置相关错误（包括规则配置错误和插件加载错误）
    if (/Configuration for rule .* is invalid/.test(output) ||
        /Failed to load config .* to extend from/.test(output) ||
        /Failed to load plugin .* declared in/.test(output) ||
        /ERROR in \[eslint\]/.test(output) ||
        /\[eslint\].*\.eslintrc/.test(output)) {
      return 'eslint-config';
    }

    // 依赖缺失错误
    if (/Module not found|Cannot resolve module/.test(output)) {
      return 'dependency-missing';
    }

    // 语法错误
    if (/SyntaxError:|TypeError:|ReferenceError:/.test(output)) {
      return 'syntax-error';
    }

    return 'generic';
  }

  /**
   * 修复 ESLint 配置错误
   */
  async fixEslintConfigError(output) {
    try {
      console.log(chalk.gray(`   🔧 修复 ESLint 配置错误`));
      const migrationSuggestions = this.generateMigrationSuggestions(output);

      const eslintrcPath = path.join(this.projectPath, '.eslintrc.js');

      if (!await fs.pathExists(eslintrcPath)) {
        throw new Error('.eslintrc.js 文件不存在');
      }

      const result = await this.buildFixAgent.fixFiles([eslintrcPath], output, 1, migrationSuggestions);

      return {
        success: result.success,
        filesModified: result.filesModified || 0,
        errorsFixed: 1,
        suggestions: migrationSuggestions,
        error: result.errors ? result.errors.join('; ') : undefined
      };
    } catch (error) {
      return {
        success: false,
        error: `修复 ESLint 配置错误失败: ${error.message}`
      };
    }
  }

  /**
   * 生成针对迁移项目的建议
   */
  generateMigrationSuggestions(output) {
    const suggestions = [];

    // 基础迁移建议
    suggestions.push('这是一个 Vue 2 到 Vue 3 的迁移项目，请优先考虑兼容性修复');

    // ESLint 相关建议
    if (/Failed to load config "plugin:vue\/vue3-essential"/.test(output)) {
      suggestions.push('检测到 Vue 3 ESLint 配置错误，建议：');
      suggestions.push('1. 注释掉配置');
      suggestions.push('2. 修改 package.json 安装对应的 Vue 3 ESLint 插件：npm install eslint-plugin-vue@latest');
      suggestions.push('3. 或者降级到 Vue 2 兼容的配置："plugin:vue/essential"');
    }

    if (/Configuration for rule .* is invalid/.test(output)) {
      suggestions.push('检测到 ESLint 规则配置错误，建议：');
      suggestions.push('1. 优先注释掉有问题的规则配置');
      suggestions.push('2. 这可能是 ESLint 5 -> ESLint 8 升级导致的规则格式变化');
      suggestions.push('3. 检查规则的新格式要求，或暂时禁用该规则');
    }

    if (/Failed to load plugin/.test(output)) {
      suggestions.push('检测到 ESLint 插件加载错误，建议：');
      suggestions.push('1. 检查 package.json 中是否安装了对应的插件');
      suggestions.push('2. 或者暂时注释掉该插件配置');
    }

    // 通用迁移建议
    suggestions.push('迁移项目修复策略：');
    suggestions.push('- 优先注释掉有问题的配置，确保项目能够启动');
    suggestions.push('- 避免大幅修改配置结构，保持向后兼容');
    suggestions.push('- 如果是依赖问题，检查 package.json 中的版本');

    return suggestions;
  }

  /**
   * 修复 Vue 编译器错误
   */
  async fixVueCompilerError(output) {
    try {
      console.log(chalk.gray('   🔧 修复 Vue 编译器 ::v-deep 警告'));

      // 这类错误通常是警告，不会阻止启动，但我们可以选择修复
      // 暂时返回成功，因为这些是警告而不是错误
      return {
        success: true,
        filesModified: 0,
        errorsFixed: 0,
        message: 'Vue 编译器警告已忽略（不影响启动）'
      };
    } catch (error) {
      return {
        success: false,
        error: `修复 Vue 编译器错误失败: ${error.message}`
      };
    }
  }

  /**
   * 修复依赖缺失错误
   */
  async fixDependencyError(output) {
    try {
      console.log(chalk.gray('   🔧 修复依赖缺失错误'));

      // 提取缺失的模块名
      const moduleMatch = output.match(/Module not found.*?'([^']+)'/);
      if (moduleMatch) {
        const moduleName = moduleMatch[1];
        console.log(chalk.gray(`   📦 缺失模块: ${moduleName}`));
      }

      // 生成依赖相关的建议
      const suggestions = [
        '这是一个 Vue 2 到 Vue 3 的迁移项目',
        '依赖缺失可能是由于版本升级导致的',
        '建议检查 package.json 中的依赖版本',
        '可能需要安装新的依赖或更新现有依赖'
      ];

      // 使用 BuildFixAgent 来分析和修复
      const result = await this.buildFixAgent.fixFiles([], output, 1, suggestions);

      return {
        success: result.success,
        filesModified: result.filesModified || 0,
        errorsFixed: 1,
        suggestions: suggestions,
        error: result.errors ? result.errors.join('; ') : undefined
      };
    } catch (error) {
      return {
        success: false,
        error: `修复依赖错误失败: ${error.message}`
      };
    }
  }

  /**
   * 修复语法错误
   */
  async fixSyntaxError(output) {
    try {
      console.log(chalk.gray('   🔧 修复语法错误'));

      const suggestions = [
        '这是一个 Vue 2 到 Vue 3 的迁移项目',
        '语法错误可能是由于 Vue 3 语法变化导致的',
        '请检查是否使用了已废弃的 Vue 2 语法',
        '建议参考 Vue 3 迁移指南进行修复'
      ];

      // 使用 BuildFixAgent 来分析和修复
      const result = await this.buildFixAgent.fixFiles([], output, 1, suggestions);

      return {
        success: result.success,
        filesModified: result.filesModified || 0,
        errorsFixed: 1,
        suggestions: suggestions,
        error: result.errors ? result.errors.join('; ') : undefined
      };
    } catch (error) {
      return {
        success: false,
        error: `修复语法错误失败: ${error.message}`
      };
    }
  }

  /**
   * 修复通用错误
   */
  async fixGenericError(output) {
    try {
      console.log(chalk.gray('   🔧 修复通用错误'));

      const suggestions = [
        '这是一个 Vue 2 到 Vue 3 的迁移项目',
        '请优先考虑兼容性修复，避免破坏性更改',
        '如果是配置问题，建议先注释掉有问题的配置',
        '确保项目能够正常启动后再逐步完善配置'
      ];

      // 使用 BuildFixAgent 来分析和修复
      const result = await this.buildFixAgent.fixFiles([], output, 1, suggestions);

      return {
        success: result.success,
        filesModified: result.filesModified || 0,
        errorsFixed: 1,
        suggestions: suggestions,
        error: result.errors ? result.errors.join('; ') : undefined
      };
    } catch (error) {
      return {
        success: false,
        error: `修复通用错误失败: ${error.message}`
      };
    }
  }

  /**
   * 获取修复统计信息
   */
  getStats() {
    return {
      ...this.fixStats,
      buildFixAgentStats: this.buildFixAgent.getFixStats()
    };
  }
}

module.exports = DevRunFixer;
